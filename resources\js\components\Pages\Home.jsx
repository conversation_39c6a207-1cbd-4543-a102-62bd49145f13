import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
    MagnifyingGlassIcon,
    PlusIcon,
    TruckIcon,
    ShieldCheckIcon,
    CurrencyDollarIcon,
    ClockIcon,
    MapPinIcon,
    CalendarIcon,
    UsersIcon,
    StarIcon
} from '@heroicons/react/24/outline';

function Home() {
    const [searchForm, setSearchForm] = useState({
        from: '',
        to: '',
        date: '',
        passengers: 1
    });

    const [featuredTrips, setFeaturedTrips] = useState([
        {
            id: 1,
            from: 'Manila',
            to: 'Baguio',
            date: '2025-07-25',
            time: '06:00 AM',
            price: 800,
            availableSeats: 3,
            driver: '<PERSON>',
            rating: 4.8,
            vehicle: 'Toyota Innova'
        },
        {
            id: 2,
            from: 'Quezon City',
            to: 'Tagaytay',
            date: '2025-07-25',
            time: '08:00 AM',
            price: 400,
            availableSeats: 2,
            driver: '<PERSON>',
            rating: 4.9,
            vehicle: 'Honda CR-V'
        },
        {
            id: 3,
            from: 'Makati',
            to: 'Batangas',
            date: '2025-07-26',
            time: '07:30 AM',
            price: 600,
            availableSeats: 4,
            driver: '<PERSON>',
            rating: 4.7,
            vehicle: 'Mitsubishi Montero'
        }
    ]);

    const handleSearchSubmit = (e) => {
        e.preventDefault();
        // Navigate to search results with form data
        console.log('Search form:', searchForm);
        // In a real app, you would navigate to search results page
    };

    const handleInputChange = (e) => {
        setSearchForm({
            ...searchForm,
            [e.target.name]: e.target.value
        });
    };

    return (
        <div className="bg-white">
            {/* Hero Section with Search */}
            <div className="relative bg-gradient-to-r from-blue-600 to-blue-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                    <div className="text-center mb-12">
                        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                            Your Journey, <span className="text-blue-200">Connected</span>
                        </h1>
                        <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                            Safe, affordable, and reliable ride-sharing and transport services.
                            Connect with your community and travel together.
                        </p>
                    </div>

                    {/* Search Form */}
                    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
                        <form onSubmit={handleSearchSubmit} className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">From</label>
                                <div className="relative">
                                    <MapPinIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                                    <input
                                        type="text"
                                        name="from"
                                        placeholder="Departure city"
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        value={searchForm.from}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">To</label>
                                <div className="relative">
                                    <MapPinIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                                    <input
                                        type="text"
                                        name="to"
                                        placeholder="Destination city"
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        value={searchForm.to}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                                <div className="relative">
                                    <CalendarIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                                    <input
                                        type="date"
                                        name="date"
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        value={searchForm.date}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Passengers</label>
                                <div className="relative">
                                    <UsersIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                                    <select
                                        name="passengers"
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        value={searchForm.passengers}
                                        onChange={handleInputChange}
                                    >
                                        <option value={1}>1 passenger</option>
                                        <option value={2}>2 passengers</option>
                                        <option value={3}>3 passengers</option>
                                        <option value={4}>4 passengers</option>
                                        <option value={5}>5+ passengers</option>
                                    </select>
                                </div>
                            </div>
                        </form>

                        <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                                type="submit"
                                onClick={handleSearchSubmit}
                                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 flex items-center justify-center"
                            >
                                <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
                                Search Rides
                            </button>
                            <Link
                                to="/create-trip"
                                className="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 flex items-center justify-center"
                            >
                                <PlusIcon className="h-5 w-5 mr-2" />
                                Offer a Ride
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            {/* Featured Trips Section */}
            <div className="py-16 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Popular Routes Today
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Join these upcoming trips or get inspired for your next journey.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                        {featuredTrips.map((trip) => (
                            <div key={trip.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                                <div className="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900">
                                            {trip.from} → {trip.to}
                                        </h3>
                                        <p className="text-sm text-gray-600">{trip.date} at {trip.time}</p>
                                    </div>
                                    <div className="text-right">
                                        <p className="text-2xl font-bold text-blue-600">₱{trip.price}</p>
                                        <p className="text-sm text-gray-600">per person</p>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                            <span className="text-sm font-medium">{trip.driver.split(' ').map(n => n[0]).join('')}</span>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{trip.driver}</p>
                                            <div className="flex items-center">
                                                <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                                                <span className="text-sm text-gray-600 ml-1">{trip.rating}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="text-sm text-gray-600">{trip.vehicle}</p>
                                        <p className="text-sm font-medium text-green-600">{trip.availableSeats} seats left</p>
                                    </div>
                                </div>

                                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                                    Book Now
                                </button>
                            </div>
                        ))}
                    </div>

                    <div className="text-center">
                        <Link
                            to="/search"
                            className="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 font-medium rounded-md hover:bg-blue-50 transition-colors"
                        >
                            View All Available Trips
                            <MagnifyingGlassIcon className="ml-2 h-5 w-5" />
                        </Link>
                    </div>
                </div>
            </div>

            {/* Features Section */}
            <div className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Why Choose AgapeConnect?
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            We're committed to providing safe, affordable, and convenient transportation solutions for everyone.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <ShieldCheckIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Safe & Secure</h3>
                            <p className="text-gray-600">
                                Verified drivers, secure payments, and real-time tracking for your peace of mind.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Affordable</h3>
                            <p className="text-gray-600">
                                Split fuel costs and save money while helping the environment through shared rides.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <ClockIcon className="h-8 w-8 text-purple-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Convenient</h3>
                            <p className="text-gray-600">
                                Book rides instantly, track your journey, and communicate with drivers seamlessly.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Statistics Section */}
            <div className="py-16 bg-blue-600">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                        <div>
                            <div className="text-4xl font-bold text-white mb-2">10,000+</div>
                            <div className="text-blue-200">Happy Travelers</div>
                        </div>
                        <div>
                            <div className="text-4xl font-bold text-white mb-2">500+</div>
                            <div className="text-blue-200">Verified Drivers</div>
                        </div>
                        <div>
                            <div className="text-4xl font-bold text-white mb-2">50+</div>
                            <div className="text-blue-200">Cities Connected</div>
                        </div>
                        <div>
                            <div className="text-4xl font-bold text-white mb-2">₱2M+</div>
                            <div className="text-blue-200">Money Saved</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Services Section */}
            <div className="py-16 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Our Services
                        </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                            <div className="flex items-center mb-4">
                                <MagnifyingGlassIcon className="h-8 w-8 text-blue-600 mr-3" />
                                <h3 className="text-2xl font-semibold text-gray-900">Ride Sharing</h3>
                            </div>
                            <p className="text-gray-600 mb-4">
                                Find or offer rides to share costs and reduce environmental impact.
                                Perfect for daily commutes, weekend trips, or special events.
                            </p>
                            <div className="flex items-center justify-between">
                                <Link to="/search" className="text-blue-600 font-semibold hover:text-blue-800">
                                    Explore Rides →
                                </Link>
                                <div className="text-sm text-gray-500">
                                    Starting from ₱50
                                </div>
                            </div>
                        </div>

                        <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                            <div className="flex items-center mb-4">
                                <TruckIcon className="h-8 w-8 text-orange-600 mr-3" />
                                <h3 className="text-2xl font-semibold text-gray-900">TruckShare</h3>
                            </div>
                            <p className="text-gray-600 mb-4">
                                Need to transport goods? Book verified trucks for your cargo needs.
                                Safe, reliable, and cost-effective transport solutions.
                            </p>
                            <div className="flex items-center justify-between">
                                <Link to="/truckshare" className="text-orange-600 font-semibold hover:text-orange-800">
                                    Book Transport →
                                </Link>
                                <div className="text-sm text-gray-500">
                                    Starting from ₱200
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Testimonials Section */}
            <div className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            What Our Users Say
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Join thousands of satisfied travelers who trust AgapeConnect for their journeys.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="bg-gray-50 p-6 rounded-lg">
                            <div className="flex items-center mb-4">
                                {[...Array(5)].map((_, i) => (
                                    <StarIcon key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                                ))}
                            </div>
                            <p className="text-gray-600 mb-4">
                                "AgapeConnect made my daily commute so much easier and cheaper. The drivers are friendly and the app is very user-friendly!"
                            </p>
                            <div className="flex items-center">
                                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-white font-medium">AS</span>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Anna Santos</p>
                                    <p className="text-sm text-gray-600">Regular Commuter</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-lg">
                            <div className="flex items-center mb-4">
                                {[...Array(5)].map((_, i) => (
                                    <StarIcon key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                                ))}
                            </div>
                            <p className="text-gray-600 mb-4">
                                "As a driver, I love how easy it is to connect with passengers. The payment system is secure and transparent."
                            </p>
                            <div className="flex items-center">
                                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-white font-medium">MR</span>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Miguel Reyes</p>
                                    <p className="text-sm text-gray-600">Driver Partner</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-lg">
                            <div className="flex items-center mb-4">
                                {[...Array(5)].map((_, i) => (
                                    <StarIcon key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                                ))}
                            </div>
                            <p className="text-gray-600 mb-4">
                                "Perfect for weekend trips! I've met great people and saved so much money on travel expenses."
                            </p>
                            <div className="flex items-center">
                                <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-white font-medium">LC</span>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Lisa Cruz</p>
                                    <p className="text-sm text-gray-600">Weekend Traveler</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* CTA Section */}
            <div className="bg-blue-600">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                    <div className="text-center">
                        <h2 className="text-3xl font-bold text-white mb-4">
                            Ready to Get Started?
                        </h2>
                        <p className="text-xl text-blue-100 mb-8">
                            Join thousands of users who trust AgapeConnect for their transportation needs.
                        </p>
                        <Link
                            to="/register"
                            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 inline-block"
                        >
                            Sign Up Today
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Home;
