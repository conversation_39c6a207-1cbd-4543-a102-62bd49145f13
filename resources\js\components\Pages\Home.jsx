import React from 'react';
import { Link } from 'react-router-dom';
import { 
    MagnifyingGlassIcon, 
    PlusIcon, 
    TruckIcon,
    ShieldCheckIcon,
    CurrencyDollarIcon,
    ClockIcon
} from '@heroicons/react/24/outline';

function Home() {
    return (
        <div className="bg-white">
            {/* Hero Section */}
            <div className="relative bg-gradient-to-r from-blue-600 to-blue-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                            Your Journey, <span className="text-blue-200">Connected</span>
                        </h1>
                        <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                            Safe, affordable, and reliable ride-sharing and transport services. 
                            Connect with your community and travel together.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link 
                                to="/search" 
                                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 flex items-center justify-center"
                            >
                                <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
                                Find a Ride
                            </Link>
                            <Link 
                                to="/create-trip" 
                                className="bg-blue-500 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-400 flex items-center justify-center"
                            >
                                <PlusIcon className="h-5 w-5 mr-2" />
                                Offer a Ride
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            {/* Features Section */}
            <div className="py-16 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Why Choose AgapeConnect?
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            We're committed to providing safe, affordable, and convenient transportation solutions for everyone.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <ShieldCheckIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Safe & Secure</h3>
                            <p className="text-gray-600">
                                Verified drivers, secure payments, and real-time tracking for your peace of mind.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Affordable</h3>
                            <p className="text-gray-600">
                                Split fuel costs and save money while helping the environment through shared rides.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <ClockIcon className="h-8 w-8 text-purple-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Convenient</h3>
                            <p className="text-gray-600">
                                Book rides instantly, track your journey, and communicate with drivers seamlessly.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Services Section */}
            <div className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">
                            Our Services
                        </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="bg-blue-50 p-8 rounded-lg">
                            <div className="flex items-center mb-4">
                                <MagnifyingGlassIcon className="h-8 w-8 text-blue-600 mr-3" />
                                <h3 className="text-2xl font-semibold text-gray-900">Ride Sharing</h3>
                            </div>
                            <p className="text-gray-600 mb-4">
                                Find or offer rides to share costs and reduce environmental impact. 
                                Perfect for daily commutes, weekend trips, or special events.
                            </p>
                            <Link to="/search" className="text-blue-600 font-semibold hover:text-blue-800">
                                Explore Rides →
                            </Link>
                        </div>

                        <div className="bg-orange-50 p-8 rounded-lg">
                            <div className="flex items-center mb-4">
                                <TruckIcon className="h-8 w-8 text-orange-600 mr-3" />
                                <h3 className="text-2xl font-semibold text-gray-900">TruckShare</h3>
                            </div>
                            <p className="text-gray-600 mb-4">
                                Need to transport goods? Book verified trucks for your cargo needs. 
                                Safe, reliable, and cost-effective transport solutions.
                            </p>
                            <Link to="/truckshare" className="text-orange-600 font-semibold hover:text-orange-800">
                                Book Transport →
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            {/* CTA Section */}
            <div className="bg-blue-600">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                    <div className="text-center">
                        <h2 className="text-3xl font-bold text-white mb-4">
                            Ready to Get Started?
                        </h2>
                        <p className="text-xl text-blue-100 mb-8">
                            Join thousands of users who trust AgapeConnect for their transportation needs.
                        </p>
                        <Link 
                            to="/register" 
                            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 inline-block"
                        >
                            Sign Up Today
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Home;
