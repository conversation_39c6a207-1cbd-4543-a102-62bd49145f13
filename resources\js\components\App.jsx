import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './Layout/Layout';
import Home from './Pages/Home';
import Login from './Auth/Login';
import Register from './Auth/Register';
import Dashboard from './Dashboard/Dashboard';
import TripSearch from './Trips/TripSearch';
import CreateTrip from './Trips/CreateTrip';
import BookingHistory from './Bookings/BookingHistory';
import Messages from './Messages/Messages';
import Profile from './Profile/Profile';
import TruckShare from './TruckShare/TruckShare';
import AdminDashboard from './Admin/AdminDashboard';

function App() {
    return (
        <div className="min-h-screen bg-gray-50">
            <div className="p-8">
                <h1 className="text-4xl font-bold text-blue-600">React App is Working!</h1>
                <p className="text-lg mt-4">If you can see this, React is loading properly.</p>
            </div>
            <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Layout><Home /></Layout>} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />

                {/* Protected Routes */}
                <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
                <Route path="/search" element={<Layout><TripSearch /></Layout>} />
                <Route path="/create-trip" element={<Layout><CreateTrip /></Layout>} />
                <Route path="/bookings" element={<Layout><BookingHistory /></Layout>} />
                <Route path="/messages" element={<Layout><Messages /></Layout>} />
                <Route path="/profile" element={<Layout><Profile /></Layout>} />
                <Route path="/truckshare" element={<Layout><TruckShare /></Layout>} />

                {/* Admin Routes */}
                <Route path="/admin" element={<Layout><AdminDashboard /></Layout>} />
            </Routes>
        </div>
    );
}

export default App;
