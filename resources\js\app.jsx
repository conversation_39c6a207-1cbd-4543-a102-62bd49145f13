import './bootstrap';
import React from 'react';
import { createRoot } from 'react-dom/client';

// Simple test component
function TestApp() {
    return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h1 style={{ color: 'blue', fontSize: '32px' }}>🎉 React is Working!</h1>
            <p style={{ fontSize: '18px', marginTop: '10px' }}>
                If you can see this message, <PERSON>act is loading properly.
            </p>
            <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
                <strong>Next steps:</strong> The React app is working, now we can load your Home.jsx component.
            </div>
        </div>
    );
}

const container = document.getElementById('app');
if (container) {
    const root = createRoot(container);
    root.render(<TestApp />);
} else {
    console.error('Could not find app container element');
}
