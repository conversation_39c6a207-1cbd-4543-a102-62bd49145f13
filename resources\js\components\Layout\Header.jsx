import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
    Bars3Icon, 
    XMarkIcon, 
    UserIcon, 
    MagnifyingGlassIcon,
    PlusIcon,
    ChatBubbleLeftRightIcon,
    ClockIcon,
    TruckIcon
} from '@heroicons/react/24/outline';

function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isLoggedIn, setIsLoggedIn] = useState(false); // This will be managed by auth context
    const navigate = useNavigate();

    const handleLogout = () => {
        // Implement logout logic
        setIsLoggedIn(false);
        navigate('/');
    };

    return (
        <header className="bg-white shadow-lg">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Logo */}
                    <div className="flex-shrink-0">
                        <Link to="/" className="flex items-center">
                            <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-lg">A</span>
                            </div>
                            <span className="ml-2 text-xl font-bold text-gray-900">AgapeConnect</span>
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex space-x-8">
                        {isLoggedIn ? (
                            <>
                                <Link to="/search" className="flex items-center text-gray-700 hover:text-blue-600">
                                    <MagnifyingGlassIcon className="h-5 w-5 mr-1" />
                                    Find Rides
                                </Link>
                                <Link to="/create-trip" className="flex items-center text-gray-700 hover:text-blue-600">
                                    <PlusIcon className="h-5 w-5 mr-1" />
                                    Offer Ride
                                </Link>
                                <Link to="/truckshare" className="flex items-center text-gray-700 hover:text-blue-600">
                                    <TruckIcon className="h-5 w-5 mr-1" />
                                    TruckShare
                                </Link>
                                <Link to="/messages" className="flex items-center text-gray-700 hover:text-blue-600">
                                    <ChatBubbleLeftRightIcon className="h-5 w-5 mr-1" />
                                    Messages
                                </Link>
                                <Link to="/bookings" className="flex items-center text-gray-700 hover:text-blue-600">
                                    <ClockIcon className="h-5 w-5 mr-1" />
                                    My Trips
                                </Link>
                            </>
                        ) : (
                            <>
                                <Link to="/search" className="text-gray-700 hover:text-blue-600">
                                    Find Rides
                                </Link>
                                <Link to="/truckshare" className="text-gray-700 hover:text-blue-600">
                                    TruckShare
                                </Link>
                            </>
                        )}
                    </nav>

                    {/* User Menu */}
                    <div className="hidden md:flex items-center space-x-4">
                        {isLoggedIn ? (
                            <div className="relative">
                                <button className="flex items-center text-gray-700 hover:text-blue-600">
                                    <UserIcon className="h-6 w-6" />
                                    <span className="ml-1">Profile</span>
                                </button>
                                {/* Dropdown menu would go here */}
                            </div>
                        ) : (
                            <>
                                <Link to="/login" className="text-gray-700 hover:text-blue-600">
                                    Login
                                </Link>
                                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    Sign Up
                                </Link>
                            </>
                        )}
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="text-gray-700 hover:text-blue-600"
                        >
                            {isMenuOpen ? (
                                <XMarkIcon className="h-6 w-6" />
                            ) : (
                                <Bars3Icon className="h-6 w-6" />
                            )}
                        </button>
                    </div>
                </div>

                {/* Mobile Navigation */}
                {isMenuOpen && (
                    <div className="md:hidden">
                        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                            {isLoggedIn ? (
                                <>
                                    <Link to="/search" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        Find Rides
                                    </Link>
                                    <Link to="/create-trip" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        Offer Ride
                                    </Link>
                                    <Link to="/truckshare" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        TruckShare
                                    </Link>
                                    <Link to="/messages" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        Messages
                                    </Link>
                                    <Link to="/bookings" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        My Trips
                                    </Link>
                                    <Link to="/profile" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        Profile
                                    </Link>
                                    <button 
                                        onClick={handleLogout}
                                        className="block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600"
                                    >
                                        Logout
                                    </button>
                                </>
                            ) : (
                                <>
                                    <Link to="/search" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        Find Rides
                                    </Link>
                                    <Link to="/truckshare" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        TruckShare
                                    </Link>
                                    <Link to="/login" className="block px-3 py-2 text-gray-700 hover:text-blue-600">
                                        Login
                                    </Link>
                                    <Link to="/register" className="block px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                        Sign Up
                                    </Link>
                                </>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
}

export default Header;
